import { useEffect, useState, useRef } from "react";
import { Link } from "react-router-dom";
import { Container, Row, Col, Image, ListGroup } from "react-bootstrap";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import "animate.css";
import directhire1 from "../assets/directhire1.png";
import directhire2 from "../assets/directhire2.png";
import directhire3 from "../assets/directhire3.png";
import directhire4 from "../assets/directhire4.jpg";
gsap.registerPlugin(ScrollTrigger);

const directHireData = {
  hero: {
    title: "Direct Hire Staffing Services",
    subtitle:
      "Permanent Placement Solutions for Long-Term Success. We help you source, recruit, screen, and place full-time professionals who will drive your business forward with speed, accuracy, and efficiency.",
    backgroundImage:
     directhire1,
  },
  sections: [
    {
      id: "benefits",
      title: "Benefits of Direct Hire Staffing",
      texts: [""],
      listItems: [
        "Permanent Solutions: Build a stable workforce with employees committed to your organization's long-term success and growth.",
        "Quality Talent: Access to our global database of over 8 million qualified candidates across all industries and skill levels.",
        "Time Savings: Our experienced recruiters handle the entire hiring process, allowing you to focus on running your business.",
        "Cost-Effective: Reduce overall recruitment costs with our efficient screening and placement process.",
        "Cultural Fit: Thorough vetting process ensures candidates align with your company culture and values.",
        "Reduced Turnover: Higher retention rates with carefully matched permanent placements.",
      ],
      image: directhire2,
      alt: "Benefits of direct hire staffing services",
      reversed: false,
    },
    {
      id: "process",
      title: "Our Direct Hire Process",
      texts: [""],
      listItems: [
        "Consultation & Strategy: We work with you to understand your specific needs, company culture, and long-term objectives.",
        "Job Analysis: Detailed analysis of role requirements, skills needed, and ideal candidate profile.",
        "Candidate Sourcing: Leverage our extensive network and AI-powered matching technology to find the best candidates.",
        "Comprehensive Screening: Skills validation, background checks, reference verification, and cultural fit assessment.",
        "Interview Coordination: Manage the entire interview process and provide detailed candidate evaluations.",
        "Offer Management: Handle negotiations and ensure smooth onboarding of your new permanent employee.",
      ],
      image: directhire3,
      alt: "Direct hire staffing process",
      reversed: true,
    },
    {
      id: "why-work-with-us",
      title: "Why Choose Our Direct Hire Services?",
      texts: [""],
      listItems: [
        "Proven Expertise: Over 50 years of experience in permanent placement across Fortune 100 companies.",
        "Advanced Technology: AI-powered Applicant Tracking System for speed and accuracy in candidate matching.",
        "Industry Specialists: Deep knowledge across IT, healthcare, finance, engineering, and professional services.",
        "Flexible Search Options: Contingent, retained, and hybrid search services tailored to your needs.",
        "Compliance Excellence: Full compliance management for background checks, screening, and regulatory requirements.",
        "Dedicated Support: Single point of contact with dedicated account management throughout the process.",
      ],
      image: directhire4,
      alt: "Why choose our direct hire services",
      reversed: false,
    },
  ],
};

const DirectHireStaffingPage = () => {
  const heroRef = useRef(null);
  const sectionsRef = useRef([]);

  useEffect(() => {
    window.scrollTo(0, 0);

    // Hero animation
    if (heroRef.current) {
      gsap.fromTo(
        heroRef.current.querySelectorAll(".hero-content > *"),
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          stagger: 0.2,
          ease: "power3.out",
        }
      );
    }

    // Section animations
    sectionsRef.current.forEach((section, index) => {
      if (section) {
        gsap.fromTo(
          section.querySelectorAll(".animate-in"),
          { opacity: 0, y: 30 },
          {
            opacity: 1,
            y: 0,
            duration: 0.8,
            stagger: 0.1,
            ease: "power2.out",
            scrollTrigger: {
              trigger: section,
              start: "top 80%",
              end: "bottom 20%",
              toggleActions: "play none none reverse",
            },
          }
        );
      }
    });

    return () => {
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  const addToRefs = (el) => {
    if (el && !sectionsRef.current.includes(el)) {
      sectionsRef.current.push(el);
    }
  };

  return (
    <div className="direct-hire-page">
      {/* Hero Section */}
      <section
        ref={heroRef}
        className="hero-section position-relative d-flex align-items-center justify-content-center text-center text-white overflow-hidden"
        style={{
          minHeight: "100vh",
          background: `linear-gradient(135deg, rgba(0, 41, 86, 0.9) 100%), url(${directHireData.hero.backgroundImage})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundAttachment: "fixed",
        }}
      >

        <Container className="position-relative z-index-2">
          <div className="hero-content">
            <h1
              className="display-3 fw-bold mb-4"
              style={{
                fontSize: "clamp(2.5rem, 6vw, 4rem)",
                lineHeight: "1.2",
                background:
                  "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent",
                backgroundClip: "text",
                textShadow: "0 0 40px rgba(0, 160, 233, 0.4)",
                paddingBottom: "1rem",
              }}
            >
              {directHireData.hero.title}
            </h1>
            <p
              className="lead mb-5 mx-auto"
              style={{
                maxWidth: "800px",
                fontSize: "clamp(1.1rem, 2.5vw, 1.4rem)",
                lineHeight: "1.6",
                color: "rgba(255, 255, 255, 0.9)",
              }}
            >
              {directHireData.hero.subtitle}
            </p>
            <div className="d-flex flex-column flex-sm-row gap-3 justify-content-center">
             
             
            </div>
          </div>
        </Container>
      </section>

      {/* Main Content Sections */}
      <div
        className="main-content"
        style={{
          background: "linear-gradient(135deg, #002956 0%, #001a35 50%, #002956 100%)",
          minHeight: "100vh",
        }}
      >
        <Container>
          {directHireData.sections.map((section, idx) => (
            <section
              key={section.id}
              ref={addToRefs}
              className="mb-5 mb-md-6 py-3"
            >
              {/* Centered Heading Above Content */}
              <Row>
                <Col xs={12}>
                  <h2
                    className="text-center"
                    style={{
                      fontSize: "clamp(1.5rem, 4vw, 2.25rem)",
                      fontWeight: "800",
                      letterSpacing: "2.6px",
                      paddingBottom: "1.5rem",
                      marginTop: "1.5rem",
                      background:
                        "linear-gradient(135deg, #ffffff 0%, #00a0e9 100%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                      textShadow: "0 0 30px rgba(0, 160, 233, 0.3)",
                    }}
                  >
                    {section.title}
                  </h2>
                  {/* Enhanced Accent Line */}
                  <div className="w-30 h-1 mx-auto relative mb-5">
                    <div
                      className="w-full h-full rounded-sm shadow-glow"
                      style={{
                        background:
                          "linear-gradient(90deg, transparent, #00a0e9, transparent)",
                      }}
                    />
                  </div>
                </Col>
              </Row>

              {/* Content and Image Row */}
              <Row
                className={`align-items-center g-4 g-lg-5 ${
                  section.reversed ? "flex-row-reverse" : ""
                }`}
              >
                <Col
                  lg={6}
                  md={12}
                  className={`${section.reversed ? "ps-lg-4" : "pe-lg-4"}`}
                >
                  <div className="content-wrapper">
                    {section.texts.map((text, textIdx) => (
                      <p
                        key={textIdx}
                        className="mb-3 mb-md-4 animate-in"
                        style={{
                          fontSize: "1.2rem",
                          lineHeight: "1.7",
                          color: "rgba(255, 255, 255, 0.9)",
                        }}
                      >
                        {text}
                      </p>
                    ))}

                    {section.listItems && (
                      <ListGroup
                        variant="flush"
                        className="bg-transparent animate-in"
                      >
                        {section.listItems.map((item, itemIdx) => (
                          <ListGroup.Item
                            key={itemIdx}
                            className="bg-transparent border-0 px-0 py-2"
                            style={{
                              color: "rgba(255, 255, 255, 0.85)",
                              fontSize: "1.1rem",
                              lineHeight: "1.6",
                            }}
                          >
                            <div className="d-flex align-items-start">
                              <span
                                className="me-3 mt-1 flex-shrink-0"
                                style={{
                                  width: "8px",
                                  height: "8px",
                                  background: "#00a0e9",
                                  borderRadius: "50%",
                                  display: "inline-block",
                                }}
                              />
                              <span>{item}</span>
                            </div>
                          </ListGroup.Item>
                        ))}
                      </ListGroup>
                    )}
                  </div>
                </Col>

                <Col lg={6} md={12} className="text-center">
                  <div
                    className="image-wrapper animate-in"
                    style={{
                      borderRadius: "20px",
                      overflow: "hidden",
                      boxShadow: "0 20px 60px rgba(0, 160, 233, 0.2)",
                      background:
                        "linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(0,160,233,0.1) 100%)",
                      padding: "2px",
                    }}
                  >
                    <Image
                      src={section.image}
                      alt={section.alt}
                      fluid
                      style={{
                        borderRadius: "18px",
                        width: "100%",
                        height: "auto",
                        objectFit: "cover",
                      }}
                    />
                  </div>
                </Col>
              </Row>
            </section>
          ))}
        </Container>
      </div>

     

      {/* Floating Animation Styles */}
      <style>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-20px);
          }
        }

        .shadow-glow {
          box-shadow: 0 0 20px rgba(0, 160, 233, 0.5);
        }

        .z-index-2 {
          z-index: 2;
        }
      `}</style>
    </div>
  );
};

export default DirectHireStaffingPage;
