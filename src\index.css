@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    overflow-x: hidden; /* Prevent horizontal scrolling */
  }

  body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #ffffff;
    line-height: 1.6;
    min-height: 100vh;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    overflow-x: hidden; /* Prevent horizontal scrolling */
    padding-top: 72px; /* Account for fixed header */
  }

  h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
    font-weight: 600;
  }

  p {
    line-height: 1.75;
  }

  a {
    transition: all 0.3s ease;
  }

  button {
    transition: all 0.3s ease;
  }

  /* Prevent excessive spacing */
  .py-5 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  .py-6 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .py-7 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }

  /* Ensure consistent scroll behavior */
  html {
    scroll-padding-top: 80px; /* Account for fixed header */
  }
}

@layer components {
  /* Makonis Button Components */
  .btn-makonis-primary {
    @apply bg-makonis-gradient text-white font-semibold py-3 px-6 rounded-xl shadow-makonis hover:shadow-makonis-lg transform hover:-translate-y-1 transition-all duration-300 ease-out;
  }

  .btn-makonis-secondary {
    @apply bg-transparent border-2 border-makonis-secondary text-makonis-secondary font-semibold py-3 px-6 rounded-xl hover:bg-makonis-secondary hover:text-white transition-all duration-300 ease-out;
  }

  .btn-makonis-ghost {
    @apply bg-white/10 backdrop-blur-sm text-white font-semibold py-3 px-6 rounded-xl border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-300 ease-out;
  }

  /* Card Components */
  .card-makonis {
    @apply bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl shadow-makonis hover:shadow-makonis-lg transform hover:-translate-y-2 transition-all duration-500 ease-out;
  }

  .card-makonis-glass {
    @apply backdrop-blur-2xl border border-white/20 rounded-3xl shadow-2xl;
    background: rgba(255, 255, 255, 0.08);
  }

  /* Text Gradients */
  .text-makonis-gradient {
    @apply bg-makonis-gradient bg-clip-text text-transparent;
  }

  .text-makonis-gradient-reverse {
    @apply bg-makonis-gradient-reverse bg-clip-text text-transparent;
  }

  /* Container Improvements */
  .container-makonis {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Section Spacing - Reduced for better spacing */
  .section-padding {
    @apply py-8 lg:py-12;
  }

  .section-padding-sm {
    @apply py-6 lg:py-8;
  }

  .section-padding-xs {
    @apply py-4 lg:py-6;
  }

  /* Hero Section Spacing */
  .hero-padding {
    @apply pt-20 pb-12 lg:pt-24 lg:pb-16;
  }

  /* Consistent section gaps */
  .section-gap {
    @apply mb-8 lg:mb-12;
  }

  .section-gap-sm {
    @apply mb-6 lg:mb-8;
  }

  /* Header Navigation Components */
  .nav-link-enhanced {
    @apply text-makonis-primary font-semibold hover:text-makonis-secondary transition-all duration-300 relative;
  }

  .nav-link-enhanced::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-makonis-secondary transition-all duration-300;
  }

  .nav-link-enhanced:hover::after {
    @apply w-full;
  }

  .dropdown-menu-enhanced {
    @apply absolute top-full mt-2 w-56 bg-white/95 backdrop-blur-xl rounded-2xl shadow-makonis border border-gray-100 py-2 animate-slide-down;
  }

  .dropdown-item-enhanced {
    @apply flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-makonis-secondary/10 hover:text-makonis-primary transition-all duration-200 mx-2 rounded-xl;
  }

  .search-button-enhanced {
    @apply w-10 h-10 bg-makonis-secondary/10 hover:bg-makonis-secondary/20 text-makonis-primary hover:text-makonis-secondary rounded-xl flex items-center justify-center transition-all duration-300 hover:scale-110;
  }

  .mobile-menu-button-enhanced {
    @apply w-10 h-10 bg-makonis-secondary/10 hover:bg-makonis-secondary/20 text-makonis-primary hover:text-makonis-secondary rounded-xl flex items-center justify-center transition-all duration-300;
  }

  .hamburger-line {
    @apply absolute w-5 h-0.5 bg-current transition-all duration-300;
  }

  .mobile-nav-link {
    @apply flex items-center text-makonis-primary font-semibold hover:text-makonis-secondary transition-all duration-300 py-2 px-4 rounded-xl hover:bg-makonis-secondary/10;
  }

  .mobile-nav-button {
    @apply w-full text-left text-makonis-primary font-semibold hover:text-makonis-secondary transition-all duration-300 py-2 px-4 rounded-xl hover:bg-makonis-secondary/10;
  }

  .mobile-submenu {
    @apply ml-6 mt-2 space-y-1 animate-slide-down;
  }

  .mobile-submenu-item {
    @apply flex items-center py-2 px-4 text-sm text-gray-600 hover:text-makonis-secondary hover:bg-makonis-secondary/5 rounded-lg transition-all duration-200;
  }

  /* Search Components */
  .search-bar-container {
    @apply w-full max-w-lg;
  }

  .search-input-enhanced {
    @apply w-full px-5 py-3 border-2 border-gray-200 rounded-3xl text-base transition-all duration-300 bg-white/95 backdrop-blur-sm;
  }

  .search-input-enhanced:focus {
    @apply border-makonis-secondary ring-4 ring-makonis-secondary/20 bg-white outline-none;
  }

  .search-dropdown-enhanced {
    @apply absolute top-full left-0 right-0 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-2xl shadow-makonis-lg z-50 max-h-96 overflow-y-auto mt-2 border-t-4 border-t-makonis-secondary animate-slide-down;
  }

  /* Enhanced search dropdown for header */
  .search-inline-desktop .search-dropdown-enhanced {
    @apply shadow-2xl border-makonis-primary/20;
    box-shadow: 0 20px 40px rgba(0, 41, 86, 0.15), 0 8px 16px rgba(0, 160, 233, 0.1);
  }

  /* Mobile search styling */
  .search-mobile .search-input-enhanced {
    @apply text-white placeholder-white/70;
  }

  .search-mobile .search-input-enhanced:focus {
    @apply border-makonis-secondary ring-4 ring-makonis-secondary/20 bg-white/10;
  }

  .search-mobile .search-dropdown-enhanced .search-item-title-enhanced {
    @apply text-white;
  }

  .search-mobile .search-dropdown-enhanced .search-item-type-enhanced {
    @apply text-white/70;
  }

  /* Search Overlay Animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
  }

  .search-dropdown-header-enhanced {
    @apply px-4 py-3 text-sm font-semibold text-gray-600 bg-gray-50/80 border-b border-gray-200 rounded-t-2xl;
  }

  .search-dropdown-item-enhanced {
    @apply flex items-center px-4 py-3 cursor-pointer transition-all duration-200 border-b border-gray-100 hover:bg-makonis-secondary/5 hover:border-l-4 hover:border-l-makonis-secondary hover:pl-3;
  }

  .search-dropdown-item-enhanced:last-child {
    @apply border-b-0 rounded-b-2xl;
  }

  .search-item-icon-enhanced {
    @apply w-10 h-10 flex items-center justify-center bg-makonis-gradient text-white rounded-lg mr-3 text-base flex-shrink-0;
  }

  .search-dropdown-item-enhanced.selected .search-item-icon-enhanced {
    @apply bg-makonis-gradient-reverse scale-105;
  }

  .search-item-content-enhanced {
    @apply flex-1 min-w-0;
  }

  .search-item-title-enhanced {
    @apply text-sm font-semibold text-gray-900 mb-0.5 truncate;
  }

  .search-item-type-enhanced {
    @apply text-xs text-gray-500 font-medium;
  }

  .search-item-arrow-enhanced {
    @apply text-gray-400 text-xs transition-all duration-200 flex-shrink-0;
  }

  .search-dropdown-item-enhanced:hover .search-item-arrow-enhanced,
  .search-dropdown-item-enhanced.selected .search-item-arrow-enhanced {
    @apply text-makonis-secondary translate-x-1;
  }

  .search-dropdown-footer-enhanced {
    @apply px-4 py-2 border-t border-gray-200 bg-gray-50/80 rounded-b-2xl;
  }

  .search-view-all-enhanced {
    @apply flex items-center px-3 py-2 text-makonis-secondary text-sm font-semibold cursor-pointer rounded-lg transition-all duration-200 hover:bg-makonis-secondary/10 hover:text-makonis-primary;
  }

  .search-dropdown-empty-enhanced {
    @apply py-10 px-5 text-center text-gray-500;
  }

  .search-dropdown-empty-enhanced i {
    @apply text-3xl text-gray-400 block mb-3;
  }

  .search-highlight {
    @apply bg-yellow-200 text-yellow-800 px-1 py-0.5 rounded font-semibold;
  }

  /* Footer Components */
  .footer-link-enhanced {
    @apply flex items-center text-white/75 no-underline transition-all duration-200 hover:text-makonis-secondary hover:underline;
    text-underline-offset: 3px;
  }

  .social-icon-enhanced {
    @apply transition-all duration-300;
  }
}

@layer utilities {
  /* Custom Utilities */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  }

  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  /* Scrollbar Styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 160, 233, 0.3) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(0, 160, 233, 0.3);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 160, 233, 0.5);
  }
}


